import React, { useState } from "react"
import { ComponentRules, CSSClassesTable, UsageGuidelines } from "@/components"
import {
  Button,
  Typography,
  UploadBox,
  useUploadSingleFile,
  useUploadMultipleFile,
} from "@apollo/ui"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"

/**
 * UploadBox component
 *
 * The UploadBox component provides a drag-and-drop file upload interface with support 
 * for single and multiple files, file type restrictions, size limits, upload progress 
 * indicators, and comprehensive validation.
 *
 * Features:
 * - Single and multiple file upload support
 * - Drag and drop functionality
 * - File type validation with customizable extensions
 * - File size validation with configurable limits
 * - Upload progress indicators
 * - Error handling and validation messages
 * - Full width layout support
 * - Integration with form fields
 */
const meta = {
  title: "@apollo∕ui/Components/Inputs/UploadBox",
  component: UploadBox,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=3565-6509&m=dev",
    },
    docs: {
      description: {
        component:
          "The UploadBox component provides a comprehensive file upload interface with drag-and-drop support, validation, and progress tracking.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source 
            code={`import { UploadBox, useUploadSingleFile, useUploadMultipleFile } from "@apollo/ui"`} 
            language="tsx" 
          />
          <h2 id="uploadbox-props">Props</h2>
          <ArgTypes />
          <h2 id="uploadbox-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Always provide clear labels and helper text to guide users",
              "Set appropriate file type restrictions based on your use case",
              "Configure reasonable file size limits to prevent upload issues",
              "Use the multiple prop when users need to upload several files",
              "Provide feedback during upload progress with fileState",
              "Handle errors gracefully with clear error messages",
              "Consider using the provided hooks for easier state management",
            ]}
          />
          <h2 id="uploadbox-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              "Ensure the component is keyboard accessible for users who cannot use a mouse.",
              "Provide clear and descriptive labels for the upload box.",
              <>Use <code>multiple</code> prop to allow multiple file uploads when necessary.</>,
              <>Use <code>uploading</code> prop in <code>fileState</code> to indicate specific file upload progress.</>,
              <>Use <code>errorMessage</code> prop in <code>fileState</code> to indicate specific file upload error.</>
            ]}
          />
          <h2 id="uploadbox-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The UploadBox component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts and states of the component."
            data={[
              {
                cssClassName: ".uploadBox-formControl",
                description: "Styles applied to the form control container",
                usageNotes: "Use for overall component styling and positioning",
              },
              {
                cssClassName: ".uploadBox-uploadSection",
                description: "Styles applied to the upload section wrapper",
                usageNotes: "Contains the upload button and file list",
              },
              {
                cssClassName: ".uploadBox-fileConditionContainer",
                description: "Styles applied to the file condition container",
                usageNotes: "Contains the file type and size restrictions",
              },
              {
                cssClassName: ".uploadBox-fileConditionList",
                description: "Styles applied to the list of file conditions",
                usageNotes: "Contains the individual file type and size restrictions",
              },
              {
                cssClassName: ".uploadBox-uploadButton",
                description: "Styles applied to the upload button",
                usageNotes: "Use for customizing the button appearance",
              },
              {
                cssClassName: ".uploadBox-uploadedFileList",
                description: "Styles applied to the uploaded file list container",
                usageNotes: "Use for customizing the appearance of the uploaded file list",
              },
              {
                cssClassName: ".uploadBox-uploadedFileItem",
                description: "Styles applied to each uploaded file item",
                usageNotes: "Use for customizing the appearance of individual uploaded files",
              },
              {
                cssClassName: ".uploadBox-uploadedFileItemContent",
                description: "Styles applied to the content of an uploaded file item",
                usageNotes: "Contains the file name, delete button, and error message",
              },
              {
                cssClassName: ".uploadBox-uploadedFileItemInfo",
                description: "Styles applied to the file information section",
                usageNotes: "Contains the file name and icon",
              },
              {
                cssClassName: ".uploadBox-uploadedFileItemIcon",
                description: "Styles applied to the file icon",
                usageNotes: "Use for customizing the appearance of the file icon",
              },
              {
                cssClassName: ".uploadBox-deleteIcon",
                description: "Styles applied to the delete button icon",
                usageNotes: "Use for customizing the appearance of the delete button icon",
              },
              {
                cssClassName: ".uploadBox-uploadingText",
                description: "Styles applied to the uploading status text",
                usageNotes: "Use for customizing the appearance of the uploading status text",
              },
              {
                cssClassName: ".uploadBox-loadingIndicatorContainer",
                description: "Styles applied to the loading indicator container",
                usageNotes: "Contains the loading indicator for file uploads",
              },
              {
                cssClassName: ".uploadBox-loadingIndicator",
                description: "Styles applied to the loading indicator element",
                usageNotes: "Use for customizing the appearance of the loading indicator",
              },
            ]}
          />
          <h2 id="uploadbox-examples">Examples</h2>
          <Stories title="" />
          <h2 id="uploadbox-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ width: 400 }}>
                      <UploadBox
                        label="Profile Picture"
                        helperText="Upload a profile picture (JPG, PNG only)"
                        allowedFilesExtension={["jpg", "jpeg", "png"]}
                        maxFileSizeInBytes={2 * 1024 * 1024}
                      />
                    </div>
                  ),
                  description: "Use specific file type restrictions for targeted use cases",
                },
                negative: {
                  component: (
                    <div style={{ width: 400 }}>
                      <UploadBox
                        multiple
                        label="Profile Picture"
                        helperText="Upload profile pictures"
                        fileLimit={10}
                      />
                    </div>
                  ),
                  description: "Don't use multiple upload for single-item use cases",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    label: {
      control: { type: "text" },
      description: "Label for the upload box",
      table: {
        type: { summary: "string" },
      },
    },
    helperText: {
      control: { type: "text" },
      description: "Helper text displayed below the upload box",
      table: {
        type: { summary: "string" },
      },
    },
    disabled: {
      control: { type: "boolean" },
      description: "Whether the upload box is disabled",
      table: {
        type: { summary: "boolean" },
      },
    },
    multiple: {
      control: { type: "boolean" },
      description: "Whether multiple files can be uploaded",
      table: {
        type: { summary: "boolean" },
      },
    },
    required: {
      control: { type: "boolean" },
      description: "Whether file upload is required",
      table: {
        type: { summary: "boolean" },
      },
    },
    fullWidth: {
      control: { type: "boolean" },
      description: "Whether the upload box should take full width",
      table: {
        type: { summary: "boolean" },
      },
    },
    fileLimit: {
      control: { type: "number" },
      description: "Maximum number of files that can be uploaded",
      table: {
        type: { summary: "number" },
      },
    },
    maxFileSizeInBytes: {
      control: { type: "number" },
      description: "Maximum file size in bytes",
      table: {
        type: { summary: "number" },
      },
    },
    uploadButtonText: {
      control: { type: "text" },
      description: "Text displayed on the upload button",
      table: {
        type: { summary: "string" },
      },
    },
    allowedFilesExtension: {
      control: { type: "object" },
      description: "Array of allowed file extensions",
      table: {
        type: { summary: "string[]" },
      },
    },
    errorMessage: {
      control: { type: "text" },
      description: "Error message displayed when file upload fails",
      table: {
        type: { summary: "string" },
      },
    },
    fileState: {
      control: { type: "object" },
      description: "State of the uploaded file(s)",
      table: {
        type: { summary: "UploadBoxFileState | UploadBoxFileState[]" },
      },
    },
    onDelete: {
      control: { type: "function" },
      description: "Callback when a file is deleted",
      table: {
        type: { summary: "(fileIndex: number) => void" },
      },
    },
  },
} satisfies Meta<typeof UploadBox>

export default meta

type Story = StoryObj<typeof UploadBox>

/** Default UploadBox for single file upload */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Basic single file upload with default settings. Supports JPG, PNG, and SVG files up to 5MB.",
      },
    },
  },
  args: {
    label: "Upload File",
    helperText: "Choose a file to upload",
  },
}

/** UploadBox with multiple file support */
export const MultipleFiles: Story = {
  parameters: {
    docs: {
      description: {
        story: "Multiple file upload with file limit and management capabilities.",
      },
    },
  },
  render: (args) => {
    function MultipleFilesDemo() {
      const [files, setFiles] = useState<File[]>([])

      const handleUpload = (newFiles: File[]) => {
        setFiles((prev) => [...prev, ...newFiles])
      }

      const handleDelete = (index: number) => {
        setFiles((prev) => prev.filter((_, i) => i !== index))
      }

      return (
        <UploadBox
          multiple
          label="Upload Multiple Files"
          helperText={`Selected ${files.length} file${files.length !== 1 ? "s" : ""}`}
          value={files}
          onUpload={handleUpload}
          onDelete={handleDelete}
          fileLimit={5}
          allowedFilesExtension={args.allowedFilesExtension}
          maxFileSizeInBytes={args.maxFileSizeInBytes}
          disabled={args.disabled}
          required={args.required}
          fullWidth={args.fullWidth}
          uploadButtonText={args.uploadButtonText}
        />
      )
    }
    return <MultipleFilesDemo />
  },
}

/** UploadBox with full width layout */
export const FullWidth: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story: "UploadBox stretches to fill the width of its container.",
      },
    },
  },
  render: (args) => (
    <div style={{ width: "100%" }}>
      <UploadBox {...args} fullWidth label="Full Width Upload" />
    </div>
  ),
}

/** Comprehensive states showcase */
export const States: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story:
          "A side-by-side comparison of common UploadBox states for quick visual reference: default, disabled, error, and with different configurations.",
      },
    },
  },
  render: () => {
    function StatesDemo() {
      const [normalFile, setNormalFile] = useState<File | null>(null)
      const [errorFile, setErrorFile] = useState<File | null>(null)

      return (
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(2, minmax(300px, 1fr))",
            gap: 20,
            alignItems: "start",
          }}
        >
          <div>
            <Typography level="bodyLarge" style={{ marginBottom: 8 }}>Default</Typography>
            <UploadBox
              label="Upload Document"
              helperText="Choose a file to upload"
              value={normalFile}
              onUpload={(file) => setNormalFile(file)}
              onDelete={() => setNormalFile(null)}
            />
          </div>

          <div>
            <Typography level="bodyLarge" style={{ marginBottom: 8 }}>Disabled</Typography>
            <UploadBox
              label="Upload Document"
              helperText="Upload is disabled"
              disabled
            />
          </div>

          <div>
            <Typography level="bodyLarge" style={{ marginBottom: 8 }}>Required</Typography>
            <UploadBox
              label="Upload Document"
              helperText="This field is required"
              required
            />
          </div>

          <div>
            <Typography level="bodyLarge" style={{ marginBottom: 8 }}>With Error</Typography>
            <UploadBox
              label="Upload Document"
              helperText="Upload failed - please try again"
              value={errorFile}
              onUpload={(file) => setErrorFile(file)}
              onDelete={() => setErrorFile(null)}
              errorMessage="Upload failed due to network error"
            />
          </div>
        </div>
      )
    }
    return <StatesDemo />
  },
}

/** File type restrictions */
export const FileTypeRestrictions: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story: "UploadBox with different file type restrictions for specific use cases.",
      },
    },
  },
  render: () => {
    function FileTypeDemo() {
      const [imageFiles, setImageFiles] = useState<File[]>([])
      const [documentFile, setDocumentFile] = useState<File | null>(null)

      return (
        <div style={{ display: "flex", flexDirection: "column", gap: 24 }}>
          <div>
            <Typography level="titleSmall" style={{ marginBottom: 8 }}>Images Only</Typography>
            <UploadBox
              multiple
              label="Upload Images"
              helperText="Only JPG, PNG, and GIF files are allowed"
              value={imageFiles}
              onUpload={(files) => setImageFiles((prev) => [...prev, ...files])}
              onDelete={(index) => setImageFiles((prev) => prev.filter((_, i) => i !== index))}
              allowedFilesExtension={["jpg", "jpeg", "png", "gif"]}
              fileLimit={3}
            />
          </div>

          <div>
            <Typography level="titleSmall" style={{ marginBottom: 8 }}>Documents Only</Typography>
            <UploadBox
              label="Upload Document"
              helperText="Only PDF, DOC, and TXT files are allowed"
              value={documentFile}
              onUpload={(file) => setDocumentFile(file)}
              onDelete={() => setDocumentFile(null)}
              allowedFilesExtension={["pdf", "doc", "docx", "txt"]}
            />
          </div>
        </div>
      )
    }
    return <FileTypeDemo />
  },
}

/** Size limits demonstration */
export const SizeLimits: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story: "UploadBox with different file size limitations.",
      },
    },
  },
  render: () => {
    function SizeLimitsDemo() {
      const [smallFile, setSmallFile] = useState<File | null>(null)
      const [largeFile, setLargeFile] = useState<File | null>(null)

      return (
        <div style={{ display: "flex", flexDirection: "column", gap: 24 }}>
          <div>
            <Typography level="titleSmall" style={{ marginBottom: 8 }}>Small Files (1MB limit)</Typography>
            <UploadBox
              label="Upload Small File"
              helperText="Maximum file size: 1MB"
              value={smallFile}
              onUpload={(file) => setSmallFile(file)}
              onDelete={() => setSmallFile(null)}
              maxFileSizeInBytes={1024 * 1024} // 1MB
            />
          </div>

          <div>
            <Typography level="titleSmall" style={{ marginBottom: 8 }}>Large Files (10MB limit)</Typography>
            <UploadBox
              label="Upload Large File"
              helperText="Maximum file size: 10MB"
              value={largeFile}
              onUpload={(file) => setLargeFile(file)}
              onDelete={() => setLargeFile(null)}
              maxFileSizeInBytes={10 * 1024 * 1024} // 10MB
            />
          </div>
        </div>
      )
    }
    return <SizeLimitsDemo />
  },
}

/** Using the useUploadSingleFile hook */
export const WithSingleFileHook: Story = {
  parameters: {
    docs: {
      description: {
        story: "Demonstrates using the useUploadSingleFile hook for easier state management and upload progress tracking.",
      },
    },
  },
  render: () => {
    function SingleFileHookDemo() {
      const singleFileUpload = useUploadSingleFile({
        uploadFileFn: async (file) => {
          // Simulate an upload process
          return new Promise((resolve) => {
            setTimeout(() => {
              console.log("File uploaded:", file.name)
              resolve(file)
            }, 2000)
          })
        },
        onDelete: () => {
          console.log("File deleted")
        },
      })

      return (
        <UploadBox
          {...singleFileUpload}
          label="Upload with Hook"
          helperText="Uses useUploadSingleFile hook for state management"
          allowedFilesExtension={["pdf", "doc", "docx", "jpg", "png"]}
          maxFileSizeInBytes={5 * 1024 * 1024}
        />
      )
    }
    return <SingleFileHookDemo />
  },
}

/** Using the useUploadMultipleFile hook */
export const WithMultipleFileHook: Story = {
  parameters: {
    docs: {
      description: {
        story: "Demonstrates using the useUploadMultipleFile hook for managing multiple file uploads with progress tracking.",
      },
    },
  },
  render: () => {
    function MultipleFileHookDemo() {
      const multipleFileUpload = useUploadMultipleFile({
        uploadFileFn: async (file) => {
          // Simulate an upload process
          return new Promise((resolve) => {
            setTimeout(() => {
              console.log("File uploaded:", file.name)
              resolve(file)
            }, 1500)
          })
        },
        onDelete: (fileIndex) => {
          console.log("File deleted at index:", fileIndex)
        },
      })

      return (
        <UploadBox
          {...multipleFileUpload}
          label="Upload Multiple Files with Hook"
          helperText="Uses useUploadMultipleFile hook for state management"
          allowedFilesExtension={["pdf", "doc", "docx", "jpg", "png", "gif"]}
          maxFileSizeInBytes={5 * 1024 * 1024}
          fileLimit={4}
        />
      )
    }
    return <MultipleFileHookDemo />
  },
}

/** Custom upload button text and descriptions */
export const CustomContent: Story = {
  parameters: {
    docs: {
      description: {
        story: "UploadBox with custom upload button text and custom description rendering.",
      },
    },
  },
  render: () => {
    function CustomContentDemo() {
      const [files, setFiles] = useState<File[]>([])

      const handleUpload = (newFiles: File[]) => {
        setFiles((prev) => [...prev, ...newFiles])
      }

      const handleDelete = (index: number) => {
        setFiles((prev) => prev.filter((_, i) => i !== index))
      }

      const renderDescription = () => (
        <div style={{ textAlign: "center", padding: "20px" }}>
          <div style={{ fontSize: "48px", marginBottom: "16px" }}>📄</div>
          <Typography level="titleMedium" style={{ marginBottom: 8 }}>
            Drop your files here
          </Typography>
          <Typography level="bodyMedium" style={{ marginBottom: 12, color: "#666" }}>
            or click to browse from your computer
          </Typography>
          <Typography level="bodySmall" style={{ color: "#999" }}>
            Supports: PDF, DOC, XLS, PPT • Max 5MB per file
          </Typography>
        </div>
      )

      return (
        <UploadBox
          multiple
          label="Custom Upload Interface"
          helperText={`${files.length} file${files.length !== 1 ? "s" : ""} selected`}
          value={files}
          onUpload={handleUpload}
          onDelete={handleDelete}
          renderDescription={renderDescription}
          uploadButtonText="Choose Files"
          allowedFilesExtension={["pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx"]}
          maxFileSizeInBytes={5 * 1024 * 1024}
          fileLimit={3}
        />
      )
    }
    return <CustomContentDemo />
  },
}

/** Form integration example */
export const FormIntegration: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story: "Complete form integration with required and optional file uploads, validation, and submission handling.",
      },
    },
  },
  render: () => {
    function FormDemo() {
      const [formData, setFormData] = useState({
        resume: null as File | null,
        coverLetter: null as File | null,
        portfolio: [] as File[],
      })
      const [submitted, setSubmitted] = useState(false)
      const [errors, setErrors] = useState<Record<string, string>>({})

      const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault()

        const newErrors: Record<string, string> = {}
        if (!formData.resume) {
          newErrors.resume = "Resume is required"
        }
        if (!formData.coverLetter) {
          newErrors.coverLetter = "Cover letter is required"
        }

        setErrors(newErrors)

        if (Object.keys(newErrors).length === 0) {
          setSubmitted(true)
          setTimeout(() => setSubmitted(false), 3000)
        }
      }

      const isValid = formData.resume && formData.coverLetter

      return (
        <form
          onSubmit={handleSubmit}
          style={{
            display: "flex",
            flexDirection: "column",
            gap: 20,
            maxWidth: 500,
          }}
        >
          <Typography level="titleMedium">Job Application</Typography>

          <UploadBox
            label="Resume *"
            helperText={errors.resume || "Upload your current resume"}
            required
            value={formData.resume}
            onUpload={(file) =>
              setFormData((prev) => ({ ...prev, resume: file }))
            }
            onDelete={() => setFormData((prev) => ({ ...prev, resume: null }))}
            allowedFilesExtension={["pdf", "doc", "docx"]}
            maxFileSizeInBytes={2 * 1024 * 1024} // 2MB
            errorMessage={errors.resume}
          />

          <UploadBox
            label="Cover Letter *"
            helperText={errors.coverLetter || "Upload your cover letter"}
            required
            value={formData.coverLetter}
            onUpload={(file) =>
              setFormData((prev) => ({ ...prev, coverLetter: file }))
            }
            onDelete={() =>
              setFormData((prev) => ({ ...prev, coverLetter: null }))
            }
            allowedFilesExtension={["pdf", "doc", "docx"]}
            maxFileSizeInBytes={2 * 1024 * 1024} // 2MB
            errorMessage={errors.coverLetter}
          />

          <UploadBox
            multiple
            label="Portfolio (Optional)"
            helperText={`${formData.portfolio.length} portfolio item${formData.portfolio.length !== 1 ? "s" : ""} uploaded`}
            value={formData.portfolio}
            onUpload={(files) =>
              setFormData((prev) => ({
                ...prev,
                portfolio: [...prev.portfolio, ...files],
              }))
            }
            onDelete={(index: number) =>
              setFormData((prev) => ({
                ...prev,
                portfolio: prev.portfolio.filter((_, i) => i !== index),
              }))
            }
            allowedFilesExtension={["pdf", "jpg", "jpeg", "png", "gif"]}
            maxFileSizeInBytes={5 * 1024 * 1024} // 5MB
            fileLimit={5}
          />

          <Button
            type="submit"
            disabled={!isValid}
            style={{ marginTop: 8 }}
          >
            Submit Application
          </Button>

          {submitted && isValid && (
            <div
              style={{
                padding: 12,
                background: "#e8f5e8",
                border: "1px solid #4caf50",
                borderRadius: 4,
                fontSize: 14,
              }}
            >
              ✅ Application submitted successfully!
            </div>
          )}
        </form>
      )
    }
    return <FormDemo />
  },
}
